{"name": "pokedex", "private": true, "version": "1.0.0", "description": "Pokedex - A modern image management and identification application", "author": {"name": "Pokedex Team", "email": "<EMAIL>"}, "type": "module", "main": "dist-electron/main/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\" --ignore-path .prettierignore", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,md}\" --ignore-path .prettierignore", "type-check": "tsc --noEmit", "check-all": "npm run type-check && npm run lint && npm run format:check", "electron:dev": "electron-vite dev", "electron:build": "electron-vite build", "electron:start": "electron dist-electron/main/main.js", "electron:pack": "npm run electron:build && electron-builder --publish=never", "electron:dist": "npm run electron:build && electron-builder", "electron:dist:linux": "npm run electron:build && electron-builder --linux", "electron:dist:win": "npm run electron:build && electron-builder --win", "electron:dist:mac": "npm run electron:build && electron-builder --mac", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "rebuild:test": "npm rebuild better-sqlite3", "rebuild:electron": "npx electron-rebuild --only better-sqlite3", "rebuild:all": "npm run rebuild:test && npm run rebuild:electron", "postinstall": "echo 'Dependencies installed successfully'"}, "dependencies": {"@aws-sdk/client-s3": "^3.704.0", "@aws-sdk/lib-storage": "^3.704.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@playwright/test": "^1.53.1", "axios": "1.7.2", "better-sqlite3": "^11.10.0", "echarts": "^5.6.0", "echarts-countries-js": "^1.0.2", "framer-motion": "^12.15.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-masonry-css": "^1.0.16", "react-photo-view": "^1.2.7", "react-router-dom": "^7.6.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/better-sqlite3": "^7.6.13", "@types/echarts": "^4.9.22", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "electron": "^28.3.3", "electron-builder": "^26.0.12", "electron-rebuild": "^3.2.9", "electron-vite": "^4.0.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "prettier": "^3.6.0", "sqlite3": "^5.1.7", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^3.2.4"}, "build": {"appId": "com.pokedex.app", "productName": "Pokedex", "directories": {"buildResources": "resources", "output": "release"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*", "package.json"], "mac": {"icon": "resources/icon.icns", "category": "public.app-category.photography", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "resources/icon.ico", "artifactName": "${productName}-${version}-${arch}.${ext}", "target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}]}, "linux": {"icon": "resources/icon.png", "category": "Graphics", "target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "artifactName": "${productName}-Setup-${version}.${ext}"}}}