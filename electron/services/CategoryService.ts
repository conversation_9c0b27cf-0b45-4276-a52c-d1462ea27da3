import { DatabaseManager } from '../database';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import type { CategoryCreate, CategoryRead, CategoryUpdate, CategoryListResponse, CategoryReadWithImages } from '../../schemas/category';

export class CategoryService {
  constructor(private dbManager: DatabaseManager) {}
  
  async getCategories(skip = 0, limit = 100): Promise<CategoryListResponse> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const query = `
      SELECT id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at
      FROM categories
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const categories = db.prepare(query).all(limit, skip) as CategoryRead[];
    console.log(`获取分类: ${categories.length} 条记录 (跳过${skip}条，限制${limit}条)`);
    return categories;
  }
  
  async createCategory(categoryData: CategoryCreate): Promise<CategoryRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const insert = db.prepare(`
      INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const category: CategoryRead = {
      id,
      name: categoryData.name,
      description: categoryData.description,
      thumbnail_path: null,
      thumbnail_url: null,
      created_at: now,
      updated_at: now
    };
    
    insert.run(id, categoryData.name, categoryData.description || null, null, null, now, now);
    
    console.log('创建分类:', category.name, 'ID:', category.id);
    return category;
  }
  
  async updateCategory(categoryId: string, categoryData: CategoryUpdate): Promise<CategoryRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 构建动态更新查询，只更新提供的字段
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    if (categoryData.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(categoryData.name);
    }
    
    if (categoryData.description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(categoryData.description);
    }
    
    if (updateFields.length === 0) {
      throw new Error('没有提供需要更新的字段');
    }
    
    updateFields.push('updated_at = ?');
    const now = new Date().toISOString();
    updateValues.push(now);
    updateValues.push(categoryId);
    
    const updateQuery = `
      UPDATE categories 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;
    
    const update = db.prepare(updateQuery);
    const result = update.run(...updateValues);
    
    if (result.changes === 0) {
      throw new Error(`分类不存在: ${categoryId}`);
    }
    
    const getCategory = db.prepare(`
      SELECT * FROM categories WHERE id = ?
    `);
    
    const updatedCategory = getCategory.get(categoryId) as CategoryRead;
    console.log('更新分类:', updatedCategory.name, 'ID:', categoryId);
    return updatedCategory;
  }
  
  async deleteCategory(categoryId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 使用事务确保数据一致性
    const deleteCategoryTransaction = db.transaction(() => {
      // 删除分类
      const deleteCategory = db.prepare('DELETE FROM categories WHERE id = ?');
      const result = deleteCategory.run(categoryId);
      
      if (result.changes === 0) {
        throw new Error(`分类不存在: ${categoryId}`);
      }
      
      // 由于设置了ON DELETE CASCADE，相关图片和图片标签会自动删除
      console.log(`删除分类 ${categoryId} 及其相关数据`);
    });
    
    deleteCategoryTransaction();
  }
  
  async getCategoryById(categoryId: string): Promise<CategoryRead | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const query = db.prepare('SELECT * FROM categories WHERE id = ?');
    const category = query.get(categoryId) as CategoryRead | undefined;
    
    if (!category) {
      console.log(`分类不存在: ${categoryId}`);
      return null;
    }
    
    return category;
  }
  
  async getCategoryWithImages(categoryId: string): Promise<CategoryReadWithImages | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const categoryQuery = db.prepare('SELECT * FROM categories WHERE id = ?');
    const category = categoryQuery.get(categoryId) as CategoryRead | undefined;
    
    if (!category) {
      console.log(`分类不存在: ${categoryId}`);
      return null;
    }
    
    const imagesQuery = db.prepare(`
      SELECT * FROM images 
      WHERE category_id = ?
      ORDER BY created_at DESC
    `);
    
    const images = imagesQuery.all(categoryId) as any[];
    
    // 确保图片格式正确
    const processedImages = images.map(image => ({
      ...image,
      tags: image.tags || [],
      exif_info: image.exif_info ? JSON.parse(image.exif_info) : null,
      file_metadata: image.file_metadata ? JSON.parse(image.file_metadata) : null
    }));
    
    console.log(`分类 ${categoryId} 找到 ${processedImages.length} 张图片`);
    
    return {
      ...category,
      images: processedImages
    };
  }
}