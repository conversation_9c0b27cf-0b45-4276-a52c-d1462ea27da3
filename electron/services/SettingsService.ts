import { app } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

// OSS配置接口
interface OSSConfig {
  endpoint: string;       // OSS服务端点
  region: string;         // 区域信息
  accessKeyId: string;    // 访问密钥ID
  secretAccessKey: string; // 访问密钥Secret
  bucket: string;         // 存储桶名称
  pathPrefix?: string;    // 路径前缀（可选）
}

// 仅在Electron端定义，不涉及Zod Schema
interface StorageSettings {
  storagePath: string;           // 用户选择的存储根路径
  usesCategoryFolders: boolean;  // 是否使用分类文件夹结构  
  isFirstTimeSetup: boolean;     // 是否完成首次设置
  lastMigrationVersion: string;  // 最后迁移版本号
  createdAt: string;            // 配置创建时间
  updatedAt: string;            // 最后更新时间
  // 新增OSS支持字段
  storageType: 'local' | 'oss';  // 存储类型
  ossConfig?: OSSConfig;         // OSS配置（可选）
}

export class SettingsService {
  private settingsPath: string;
  private defaultSettings: StorageSettings;

  constructor() {
    // 设置文件存储在用户数据目录中
    const userDataPath = app.getPath('userData');
    this.settingsPath = path.join(userDataPath, 'storage-settings.json');
    
    // 默认配置
    this.defaultSettings = {
      storagePath: path.join(userDataPath, 'pokedex-storage'), // 默认存储路径
      usesCategoryFolders: true,  // 默认使用分类文件夹
      isFirstTimeSetup: false,    // 未完成首次设置
      lastMigrationVersion: '1.0.0',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // 新增OSS默认配置
      storageType: 'local',       // 默认使用本地存储
      ossConfig: undefined        // 默认不配置OSS
    };
  }

  /**
   * 获取存储设置，如果不存在则返回默认设置
   */
  getSettings(): StorageSettings {
    try {
      if (fs.existsSync(this.settingsPath)) {
        const settingsContent = fs.readFileSync(this.settingsPath, 'utf8');
        const settings = JSON.parse(settingsContent) as StorageSettings;
        
        // 验证设置数据的完整性
        const validatedSettings = this.validateSettings(settings);
        console.log('✅ 加载存储设置:', validatedSettings.storagePath);
        return validatedSettings;
      } else {
        console.log('📋 使用默认存储设置:', this.defaultSettings.storagePath);
        return { ...this.defaultSettings };
      }
    } catch (error) {
      console.error('❌ 加载存储设置失败，使用默认配置:', error);
      return { ...this.defaultSettings };
    }
  }

  /**
   * 保存存储设置到文件
   */
  saveSettings(settings: Partial<StorageSettings>): boolean {
    try {
      const currentSettings = this.getSettings();
      const updatedSettings: StorageSettings = {
        ...currentSettings,
        ...settings,
        updatedAt: new Date().toISOString()
      };

      // 验证设置数据
      const validatedSettings = this.validateSettings(updatedSettings);

      // 确保设置文件目录存在
      const settingsDir = path.dirname(this.settingsPath);
      if (!fs.existsSync(settingsDir)) {
        fs.mkdirSync(settingsDir, { recursive: true });
      }

      // 保存设置到文件
      fs.writeFileSync(this.settingsPath, JSON.stringify(validatedSettings, null, 2), 'utf8');
      console.log('✅ 保存存储设置成功:', validatedSettings.storagePath);
      return true;
    } catch (error) {
      console.error('❌ 保存存储设置失败:', error);
      return false;
    }
  }

  /**
   * 验证和修复设置数据的完整性
   */
  private validateSettings(settings: Partial<StorageSettings>): StorageSettings {
    const validatedSettings: StorageSettings = {
      storagePath: settings.storagePath || this.defaultSettings.storagePath,
      usesCategoryFolders: settings.usesCategoryFolders !== undefined ? settings.usesCategoryFolders : this.defaultSettings.usesCategoryFolders,
      isFirstTimeSetup: settings.isFirstTimeSetup !== undefined ? settings.isFirstTimeSetup : this.defaultSettings.isFirstTimeSetup,
      lastMigrationVersion: settings.lastMigrationVersion || this.defaultSettings.lastMigrationVersion,
      createdAt: settings.createdAt || this.defaultSettings.createdAt,
      updatedAt: settings.updatedAt || new Date().toISOString(),
      // 新增OSS字段验证
      storageType: settings.storageType || this.defaultSettings.storageType,
      ossConfig: settings.ossConfig || this.defaultSettings.ossConfig
    };

    // 验证存储路径是否为绝对路径
    if (!path.isAbsolute(validatedSettings.storagePath)) {
      console.warn('⚠️ 存储路径不是绝对路径，使用默认路径');
      validatedSettings.storagePath = this.defaultSettings.storagePath;
    }

    // 验证存储类型
    if (validatedSettings.storageType !== 'local' && validatedSettings.storageType !== 'oss') {
      console.warn('⚠️ 存储类型无效，使用默认类型');
      validatedSettings.storageType = this.defaultSettings.storageType;
    }

    // 验证OSS配置
    if (validatedSettings.storageType === 'oss' && !validatedSettings.ossConfig) {
      console.warn('⚠️ OSS存储类型但未配置OSS，切换到本地存储');
      validatedSettings.storageType = 'local';
    }

    return validatedSettings;
  }

  /**
   * 检查是否是首次启动（设置文件不存在）
   */
  isFirstTimeSetup(): boolean {
    const settings = this.getSettings();
    return !settings.isFirstTimeSetup || !fs.existsSync(this.settingsPath);
  }

  /**
   * 标记首次设置已完成
   */
  markFirstTimeSetupComplete(): boolean {
    return this.saveSettings({
      isFirstTimeSetup: true,
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * 获取当前存储根路径
   */
  getStoragePath(): string {
    return this.getSettings().storagePath;
  }

  /**
   * 检查是否使用分类文件夹结构
   */
  usesCategoryFolders(): boolean {
    return this.getSettings().usesCategoryFolders;
  }

  /**
   * 更新存储路径
   */
  updateStoragePath(newPath: string): boolean {
    if (!path.isAbsolute(newPath)) {
      console.error('❌ 存储路径必须是绝对路径:', newPath);
      return false;
    }

    try {
      // 确保目录存在
      if (!fs.existsSync(newPath)) {
        fs.mkdirSync(newPath, { recursive: true });
      }

      // 检查目录是否可写
      fs.accessSync(newPath, fs.constants.W_OK);

      return this.saveSettings({
        storagePath: newPath,
        updatedAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ 更新存储路径失败:', error);
      return false;
    }
  }

  /**
   * 获取应用配置目录路径（存储settings.json等配置文件）
   */
  getConfigPath(): string {
    const storagePath = this.getStoragePath();
    return path.join(storagePath, '.pokedex');
  }

  /**
   * 确保应用配置目录存在
   */
  ensureConfigDirectory(): void {
    const configPath = this.getConfigPath();
    if (!fs.existsSync(configPath)) {
      fs.mkdirSync(configPath, { recursive: true });
      console.log('✅ 创建应用配置目录:', configPath);
    }
  }

  /**
   * 获取当前存储类型
   */
  getStorageType(): 'local' | 'oss' {
    return this.getSettings().storageType;
  }

  /**
   * 设置存储类型
   */
  setStorageType(type: 'local' | 'oss'): boolean {
    return this.saveSettings({
      storageType: type,
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * 获取OSS配置
   */
  getOSSConfig(): OSSConfig | undefined {
    return this.getSettings().ossConfig;
  }

  /**
   * 设置OSS配置
   */
  setOSSConfig(config: OSSConfig): boolean {
    return this.saveSettings({
      ossConfig: config,
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * 验证OSS配置是否完整
   */
  validateOSSConfig(config: OSSConfig): boolean {
    return !!(
      config.endpoint &&
      config.region &&
      config.accessKeyId &&
      config.secretAccessKey &&
      config.bucket
    );
  }

  /**
   * 检查是否配置了OSS
   */
  isOSSConfigured(): boolean {
    const config = this.getOSSConfig();
    return config ? this.validateOSSConfig(config) : false;
  }

  /**
   * 重置设置到默认值
   */
  resetToDefaults(): boolean {
    try {
      if (fs.existsSync(this.settingsPath)) {
        fs.unlinkSync(this.settingsPath);
      }
      console.log('✅ 重置存储设置到默认值');
      return true;
    } catch (error) {
      console.error('❌ 重置存储设置失败:', error);
      return false;
    }
  }
}

// 导出接口供其他模块使用
export { StorageSettings, OSSConfig };