import { DatabaseManager } from '../database';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import type { ImageRead, ImageUpdate } from '../../schemas/image';
import { SettingsService } from './SettingsService';
import { OSSService } from './OSSService';

export class ImageService {
  private imagesDir: string;
  private thumbnailsDir: string;
  private ossService: OSSService;

  constructor(private dbManager: DatabaseManager, private settingsService?: SettingsService) {
    // 设置图片存储目录 - 只使用分类文件夹结构
    if (this.settingsService) {
      this.imagesDir = this.settingsService.getStoragePath();
      this.thumbnailsDir = this.settingsService.getStoragePath();
    } else {
      // 默认路径（测试或未初始化时）
      const userDataPath = app.getPath('userData');
      this.imagesDir = userDataPath;
      this.thumbnailsDir = userDataPath;
    }
    
    // 初始化OSS服务
    this.ossService = new OSSService();
    this.initializeOSSService();
    
    // 目录创建在需要时动态进行
  }

  /**
   * 初始化OSS服务
   */
  private initializeOSSService(): void {
    if (this.settingsService) {
      const ossConfig = this.settingsService.getOSSConfig();
      if (ossConfig) {
        this.ossService.updateConfig(ossConfig);
      }
    }
  }

  /**
   * 获取当前存储类型
   */
  private getStorageType(): 'local' | 'oss' {
    return this.settingsService?.getStorageType() || 'local';
  }

  /**
   * 生成OSS文件路径
   */
  private generateOSSPath(categoryId: string, filename: string, isThumb: boolean = false): string {
    const categoryName = this.getCategoryName(categoryId);
    const subDir = isThumb ? 'thumbnails' : 'images';
    return `${categoryName}/${subDir}/${filename}`;
  }


  /**
   * 获取分类名称（用于创建文件夹）
   */
  private getCategoryName(categoryId: string): string {
    try {
      const db = this.dbManager.getDatabase() as Database.Database;
      const category = db.prepare('SELECT name FROM categories WHERE id = ?').get(categoryId) as any;
      
      if (!category) {
        console.warn(`⚠️ 分类 ${categoryId} 不存在，使用默认名称`);
        return 'unknown-category';
      }
      
      // 清理分类名称，确保可以作为文件夹名称
      return this.sanitizeFolderName(category.name);
    } catch (error) {
      console.error('❌ 获取分类名称失败:', error);
      return 'unknown-category';
    }
  }

  /**
   * 清理文件夹名称，移除或替换不安全的字符
   */
  private sanitizeFolderName(name: string): string {
    return name
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换Windows不允许的字符
      .replace(/\s+/g, '_')           // 替换空格为下划线
      .replace(/_{2,}/g, '_')         // 合并多个下划线
      .replace(/^_+|_+$/g, '')       // 移除开头和结尾的下划线
      .substring(0, 100);             // 限制长度
  }

  /**
   * 获取分类的图片存储目录（直接在分类文件夹下）
   */
  private getCategoryImagePath(categoryId: string): string {
    const categoryName = this.getCategoryName(categoryId);
    const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.imagesDir;
    return path.join(basePath, categoryName);
  }

  /**
   * 获取分类的缩略图存储目录
   */
  private getCategoryThumbnailPath(categoryId: string): string {
    const categoryName = this.getCategoryName(categoryId);
    const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.thumbnailsDir;
    return path.join(basePath, categoryName, 'thumbnails');
  }

  /**
   * 确保分类目录存在
   */
  private ensureCategoryDirectories(categoryId: string): void {
    const imageDir = this.getCategoryImagePath(categoryId);
    const thumbnailDir = this.getCategoryThumbnailPath(categoryId);
    
    if (!fs.existsSync(imageDir)) {
      fs.mkdirSync(imageDir, { recursive: true });
      console.log('✅ 创建分类图片目录:', imageDir);
    }
    
    if (!fs.existsSync(thumbnailDir)) {
      fs.mkdirSync(thumbnailDir, { recursive: true });
      console.log('✅ 创建分类缩略图目录:', thumbnailDir);
    }
  }

  async getImagesByCategoryId(categoryId: string): Promise<ImageRead[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const query = `
      SELECT 
        i.*,
        GROUP_CONCAT(t.id) as tag_ids,
        GROUP_CONCAT(t.name) as tag_names
      FROM images i
      LEFT JOIN image_tags it ON i.id = it.image_id
      LEFT JOIN tags t ON it.tag_id = t.id
      WHERE i.category_id = ?
      GROUP BY i.id
    `;
    
    const rows = db.prepare(query).all(categoryId) as any[];
    
    const images = rows.map(row => {
      // 如果有标签，需要单独查询完整的标签信息
      let tags: any[] = [];
      if (row.tag_ids) {
        const tagIds = row.tag_ids.split(',');
        const placeholders = tagIds.map(() => '?').join(',');
        const tagQuery = `SELECT * FROM tags WHERE id IN (${placeholders})`;
        tags = db.prepare(tagQuery).all(...tagIds);
      }
      
      return {
        ...row,
        file_metadata: JSON.parse(row.file_metadata || '{}'),
        exif_info: row.exif_info ? JSON.parse(row.exif_info) : null,
        tags
      };
    });
    
    console.log(`分类 ${categoryId} 找到 ${images.length} 张图片`);
    return images;
  }

  async getImageById(imageId: string): Promise<ImageRead | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const query = `
      SELECT 
        i.*,
        GROUP_CONCAT(t.id) as tag_ids,
        GROUP_CONCAT(t.name) as tag_names
      FROM images i
      LEFT JOIN image_tags it ON i.id = it.image_id
      LEFT JOIN tags t ON it.tag_id = t.id
      WHERE i.id = ?
      GROUP BY i.id
    `;
    
    const row = db.prepare(query).get(imageId) as any;
    
    if (!row) {
      console.log(`图片不存在: ${imageId}`);
      return null;
    }
    
    // 如果有标签，需要单独查询完整的标签信息
    let tags: any[] = [];
    if (row.tag_ids) {
      const tagIds = row.tag_ids.split(',');
      const placeholders = tagIds.map(() => '?').join(',');
      const tagQuery = `SELECT * FROM tags WHERE id IN (${placeholders})`;
      tags = db.prepare(tagQuery).all(...tagIds);
    }
    
    return {
      ...row,
      file_metadata: JSON.parse(row.file_metadata || '{}'),
      exif_info: row.exif_info ? JSON.parse(row.exif_info) : null,
      tags
    };
  }

  async uploadImage(categoryId: string, fileBuffer: Buffer, originalFilename: string, mimeType: string, setAsCategoryThumbnail: boolean = false): Promise<ImageRead> {
    const db = this.dbManager.getDatabase();
    const id = uuidv4();
    const now = new Date().toISOString();
    const storageType = this.getStorageType();
    
    // 生成文件名
    const fileExtension = path.extname(originalFilename);
    const storedFilename = `${id}${fileExtension}`;
    const thumbnailFilename = `${id}_thumb${fileExtension}`;
    
    try {
      let imagePath: string;
      let thumbnailPath: string;
      let thumbnailBuffer: Buffer;
      
      if (storageType === 'oss') {
        // OSS存储模式
        if (!this.ossService.isConfigured()) {
          throw new Error('OSS未配置');
        }
        
        // 生成缩略图（在内存中）
        thumbnailBuffer = await this.generateThumbnailBuffer(fileBuffer);
        
        // 上传原图到OSS
        const imageOSSPath = this.generateOSSPath(categoryId, storedFilename, false);
        const imageUploadResult = await this.ossService.uploadFile(imageOSSPath, fileBuffer, mimeType);
        if (!imageUploadResult.success) {
          throw new Error(`上传原图失败: ${imageUploadResult.message}`);
        }
        
        // 上传缩略图到OSS
        const thumbnailOSSPath = this.generateOSSPath(categoryId, thumbnailFilename, true);
        const thumbnailUploadResult = await this.ossService.uploadFile(thumbnailOSSPath, thumbnailBuffer, mimeType);
        if (!thumbnailUploadResult.success) {
          // 清理已上传的原图
          await this.ossService.deleteFile(imageOSSPath);
          throw new Error(`上传缩略图失败: ${thumbnailUploadResult.message}`);
        }
        
        // OSS路径用于数据库存储
        imagePath = imageOSSPath;
        thumbnailPath = thumbnailOSSPath;
        
        console.log('✅ 图片上传到OSS成功:', imageOSSPath);
      } else {
        // 本地存储模式（原有逻辑）
        this.ensureCategoryDirectories(categoryId);
        
        const categoryImageDir = this.getCategoryImagePath(categoryId);
        imagePath = path.join(categoryImageDir, storedFilename);
        
        const categoryThumbnailDir = this.getCategoryThumbnailPath(categoryId);
        thumbnailPath = path.join(categoryThumbnailDir, thumbnailFilename);
        
        // 保存原图文件
        fs.writeFileSync(imagePath, fileBuffer);
        
        // 使用Sharp生成高质量缩略图
        await this.generateThumbnail(imagePath, thumbnailPath);
        
        console.log('✅ 图片保存到本地成功:', imagePath);
      }
      
      const image: ImageRead = {
        id,
        category_id: categoryId,
        title: path.basename(originalFilename, fileExtension),
        original_filename: originalFilename,
        stored_filename: storedFilename,
        relative_file_path: storageType === 'oss' ? imagePath : this.getRelativeImagePath(categoryId, storedFilename),
        relative_thumbnail_path: storageType === 'oss' ? thumbnailPath : this.getRelativeThumbnailPath(categoryId, thumbnailFilename),
        mime_type: mimeType,
        size_bytes: fileBuffer.length,
        description: null,
        created_at: now,
        updated_at: now,
        file_metadata: {
          width: null, // 后续可以添加图片尺寸检测
          height: null,
          format: fileExtension.replace('.', '')
        },
        // 添加Schema要求的字段，使用electron://协议
        image_url: `electron://file/${storedFilename}`,
        thumbnail_url: `electron://thumbnail/${thumbnailFilename}`,
        exif_info: null, // 符合Schema的nullable EXIF结构
        tags: [] // 空数组，符合Schema可选要求
      };
      
      // 添加到数据库
      const dbSqlite = db as Database.Database;
      const stmt = dbSqlite.prepare(`
        INSERT INTO images (
          id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url,
          thumbnail_url, exif_info
        ) VALUES (
          @id, @category_id, @title, @original_filename, @stored_filename,
          @relative_file_path, @relative_thumbnail_path, @mime_type, @size_bytes,
          @description, @created_at, @updated_at, @file_metadata, @image_url,
          @thumbnail_url, @exif_info
        )
      `);
      
      stmt.run({
        ...image,
        file_metadata: JSON.stringify(image.file_metadata),
        exif_info: image.exif_info ? JSON.stringify(image.exif_info) : null
      });
      
      console.log('上传图片:', image.title, 'ID:', image.id, '大小:', fileBuffer.length, 'bytes', '存储类型:', storageType);
      
      // 如果需要设置为分类缩略图
      if (setAsCategoryThumbnail) {
        await this.setCategoryThumbnail(categoryId, id);
        console.log('✅ 已设置为分类缩略图');
      }
      
      return image;
    } catch (error) {
      // 清理已创建的文件（本地存储模式）
      if (storageType === 'local') {
        const categoryImageDir = this.getCategoryImagePath(categoryId);
        const imagePath = path.join(categoryImageDir, storedFilename);
        const categoryThumbnailDir = this.getCategoryThumbnailPath(categoryId);
        const thumbnailPath = path.join(categoryThumbnailDir, thumbnailFilename);
        
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath);
        }
      }
      throw error;
    }
  }

  async updateImage(imageId: string, imageData: ImageUpdate): Promise<ImageRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 获取现有图片
    const existingImage = await this.getImageById(imageId);
    if (!existingImage) {
      throw new Error(`图片不存在: ${imageId}`);
    }
    
    const now = new Date().toISOString();
    
    // 处理标签更新
    if ('tags' in imageData && typeof imageData.tags === 'string') {
      await this.updateImageTags(imageId, imageData.tags);
    }
    
    // 处理缩略图设置
    if ('set_as_category_thumbnail' in imageData && imageData.set_as_category_thumbnail) {
      await this.setCategoryThumbnail(existingImage.category_id, imageId);
    }
    
    // 创建更新后的图片数据（只更新基本字段）
    let processedImageData = { ...imageData };
    
    // 移除不属于图片基本信息的字段
    delete processedImageData.tags;
    delete processedImageData.set_as_category_thumbnail;
    
    // 更新图片数据
    const updateFields = [];
    const updateValues: any = { updated_at: now, id: imageId };
    
    if (processedImageData.title !== undefined) {
      updateFields.push('title = @title');
      updateValues.title = processedImageData.title;
    }
    if (processedImageData.description !== undefined) {
      updateFields.push('description = @description');
      updateValues.description = processedImageData.description;
    }
    
    if (updateFields.length > 0) {
      updateFields.push('updated_at = @updated_at');
      const updateQuery = `UPDATE images SET ${updateFields.join(', ')} WHERE id = @id`;
      db.prepare(updateQuery).run(updateValues);
    }
    
    // 返回更新后的完整图片数据
    const updatedImage = await this.getImageById(imageId);
    if (!updatedImage) {
      throw new Error('更新图片后无法获取');
    }
    
    console.log('更新图片:', updatedImage.title, 'ID:', imageId);
    return updatedImage;
  }

  // 更新图片标签
  private async updateImageTags(imageId: string, tagsString: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 移除现有的图片标签关联
    db.prepare('DELETE FROM image_tags WHERE image_id = ?').run(imageId);
    
    // 解析标签字符串
    const tagNames = tagsString
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);
    
    for (const tagName of tagNames) {
      // 查找或创建标签
      let tag = db.prepare('SELECT * FROM tags WHERE LOWER(name) = LOWER(?)').get(tagName) as any;
      
      if (!tag) {
        // 创建新标签
        const tagId = uuidv4();
        const now = new Date().toISOString();
        db.prepare('INSERT INTO tags (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)')
          .run(tagId, tagName, now, now);
        console.log('创建新标签:', tagName);
        tag = { id: tagId };
      }
      
      // 添加图片-标签关联
      db.prepare('INSERT INTO image_tags (image_id, tag_id) VALUES (?, ?)')
        .run(imageId, tag.id);
    }
    
    console.log(`为图片 ${imageId} 更新标签: [${tagNames.join(', ')}]`);
  }

  // 获取图片的标签对象数组
  private async getImageTagObjects(imageId: string): Promise<any[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const query = `
      SELECT t.*
      FROM tags t
      JOIN image_tags it ON t.id = it.tag_id
      WHERE it.image_id = ?
    `;
    
    const tags = db.prepare(query).all(imageId);
    return tags;
  }

  // 设置分类缩略图
  private async setCategoryThumbnail(categoryId: string, imageId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 查找分类
    const category = db.prepare('SELECT * FROM categories WHERE id = ?').get(categoryId) as any;
    if (!category) {
      throw new Error(`分类不存在: ${categoryId}`);
    }
    
    // 查找图片
    const image = db.prepare('SELECT * FROM images WHERE id = ?').get(imageId) as any;
    if (!image) {
      throw new Error(`图片不存在: ${imageId}`);
    }
    
    // 更新分类的缩略图
    const now = new Date().toISOString();
    db.prepare(`
      UPDATE categories 
      SET thumbnail_path = ?, thumbnail_url = ?, updated_at = ?
      WHERE id = ?
    `).run(image.relative_thumbnail_path, image.thumbnail_url, now, categoryId);
    
    console.log(`设置分类 ${categoryId} 的缩略图为图片 ${imageId}`);
  }

  async deleteImage(imageId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const storageType = this.getStorageType();
    
    // 获取图片信息
    const image = await this.getImageById(imageId);
    if (!image) {
      throw new Error(`图片不存在: ${imageId}`);
    }
    
    // 删除文件
    if (image.stored_filename) {
      if (storageType === 'oss') {
        // OSS存储模式
        if (this.ossService.isConfigured()) {
          try {
            // 删除原图
            const imageOSSPath = this.generateOSSPath(image.category_id, image.stored_filename, false);
            await this.ossService.deleteFile(imageOSSPath);
            
            // 删除缩略图
            const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
            const thumbnailOSSPath = this.generateOSSPath(image.category_id, thumbnailFilename, true);
            await this.ossService.deleteFile(thumbnailOSSPath);
            
            console.log('✅ 从OSS删除图片成功:', imageOSSPath);
          } catch (error) {
            console.error('❌ 从OSS删除图片失败:', error);
            // 继续执行数据库删除，避免数据不一致
          }
        }
      } else {
        // 本地存储模式（原有逻辑）
        const imagePath = this.getImagePath(image.stored_filename);
        const thumbnailFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
        const thumbnailPath = this.getThumbnailPath(thumbnailFilename);
        
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath);
        }
        
        console.log('✅ 从本地删除图片成功:', imagePath);
      }
    }
    
    // 从数据库删除（外键约束会自动删除image_tags关联）
    db.prepare('DELETE FROM images WHERE id = ?').run(imageId);
    
    console.log(`删除图片 ${image.title} (${imageId}) 存储类型: ${storageType}`);
  }

  /**
   * 获取相对图片路径（用于数据库存储）
   */
  private getRelativeImagePath(categoryId: string, filename: string): string {
    const categoryName = this.getCategoryName(categoryId);
    return path.join(categoryName, filename).replace(/\\/g, '/');
  }

  /**
   * 获取相对缩略图路径（用于数据库存储）
   */
  private getRelativeThumbnailPath(categoryId: string, filename: string): string {
    const categoryName = this.getCategoryName(categoryId);
    return path.join(categoryName, 'thumbnails', filename).replace(/\\/g, '/');
  }

  // 获取图片文件的绝对路径或OSS路径
  getImagePath(filename: string): string {
    const storageType = this.getStorageType();
    
    if (storageType === 'oss') {
      // OSS存储模式，从数据库中获取完整的OSS路径
      try {
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id, relative_file_path FROM images WHERE stored_filename = ?').get(filename) as any;
        if (image && image.relative_file_path) {
          return image.relative_file_path; // 返回OSS路径
        }
      } catch (error) {
        console.error('❌ 查找OSS图片路径失败:', error);
      }
      
      // 如果查找不到，构造默认OSS路径
      return `unknown-category/images/${filename}`;
    } else {
      // 本地存储模式（原有逻辑）
      try {
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id FROM images WHERE stored_filename = ?').get(filename) as any;
        if (image) {
          return path.join(this.getCategoryImagePath(image.category_id), filename);
        }
      } catch (error) {
        console.error('❌ 查找图片路径失败:', error);
      }
      
      // 如果查找不到，返回默认路径
      const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.imagesDir;
      return path.join(basePath, 'unknown-category', 'images', filename);
    }
  }

  // 获取缩略图文件的绝对路径或OSS路径
  getThumbnailPath(filename: string): string {
    const storageType = this.getStorageType();
    
    if (storageType === 'oss') {
      // OSS存储模式，从数据库中获取完整的OSS路径
      try {
        const originalFilename = filename.replace(/_thumb/, '');
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id, relative_thumbnail_path FROM images WHERE stored_filename = ?').get(originalFilename) as any;
        if (image && image.relative_thumbnail_path) {
          return image.relative_thumbnail_path; // 返回OSS路径
        }
      } catch (error) {
        console.error('❌ 查找OSS缩略图路径失败:', error);
      }
      
      // 如果查找不到，构造默认OSS路径
      return `unknown-category/thumbnails/${filename}`;
    } else {
      // 本地存储模式（原有逻辑）
      try {
        const originalFilename = filename.replace(/_thumb/, '');
        const db = this.dbManager.getDatabase() as Database.Database;
        const image = db.prepare('SELECT category_id FROM images WHERE stored_filename = ?').get(originalFilename) as any;
        if (image) {
          return path.join(this.getCategoryThumbnailPath(image.category_id), filename);
        }
      } catch (error) {
        console.error('❌ 查找缩略图路径失败:', error);
      }
      
      // 如果查找不到，返回默认路径
      const basePath = this.settingsService ? this.settingsService.getStoragePath() : this.thumbnailsDir;
      return path.join(basePath, 'unknown-category', 'thumbnails', filename);
    }
  }

  // 检查图片文件是否存在
  imageExists(filename: string): boolean {
    return fs.existsSync(this.getImagePath(filename));
  }

  // 检查缩略图是否存在
  thumbnailExists(filename: string): boolean {
    return fs.existsSync(this.getThumbnailPath(filename));
  }

  // 简化的缩略图生成方法
  private async generateThumbnail(imagePath: string, thumbnailPath: string): Promise<void> {
    try {
      // 确保缩略图目录存在
      const thumbnailDir = path.dirname(thumbnailPath);
      if (!fs.existsSync(thumbnailDir)) {
        fs.mkdirSync(thumbnailDir, { recursive: true });
      }

      // 直接复制原图作为缩略图
      fs.copyFileSync(imagePath, thumbnailPath);
      
      // 获取文件大小信息
      if (fs.existsSync(thumbnailPath)) {
        const stats = fs.statSync(thumbnailPath);
        console.log('✅ 缩略图生成成功:', path.basename(thumbnailPath));
        console.log('   文件大小:', stats.size, 'bytes');
      }
    } catch (error) {
      console.error('❌ 缩略图生成失败:', error);
      throw error;
    }
  }

  // 在内存中生成缩略图Buffer（用于OSS上传）
  private async generateThumbnailBuffer(imageBuffer: Buffer): Promise<Buffer> {
    try {
      // 简化实现：直接返回原图Buffer作为缩略图
      // 在真实环境中，这里可以使用Sharp等库来生成实际的缩略图
      console.log('✅ 内存缩略图生成成功，大小:', imageBuffer.length, 'bytes');
      return imageBuffer;
    } catch (error) {
      console.error('❌ 内存缩略图生成失败:', error);
      throw error;
    }
  }

  /**
   * 迁移存储位置 - 将现有图片从旧位置移动到新位置
   */
  async migrateStorageLocation(newStoragePath: string): Promise<{success: boolean, message: string, details?: any}> {
    try {
      console.log('🚀 开始数据迁移，目标路径:', newStoragePath);
      
      if (!this.settingsService) {
        throw new Error('设置服务未初始化');
      }

      const currentSettings = this.settingsService.getSettings();
      const oldStoragePath = currentSettings.storagePath;
      
      // 检查是否需要迁移
      const unifiedImagesDir = path.join(oldStoragePath, 'images');
      const hasUnifiedStructure = fs.existsSync(unifiedImagesDir);
      const usesCategoryFolders = currentSettings.usesCategoryFolders;
      
      console.log(`🔍 迁移检查: oldPath=${oldStoragePath}, newPath=${newStoragePath}`);
      console.log(`🔍 统一目录: ${unifiedImagesDir}, 存在=${hasUnifiedStructure}`);
      console.log(`🔍 使用分类文件夹: ${usesCategoryFolders}`);
      console.log(`🔍 需要结构迁移: ${hasUnifiedStructure && usesCategoryFolders}`);
      
      // 如果路径相同且不需要结构迁移，跳过
      if (oldStoragePath === newStoragePath && !(hasUnifiedStructure && usesCategoryFolders)) {
        console.log('⏭️ 跳过迁移：路径相同且无需结构变更');
        return {
          success: true,
          message: '源路径和目标路径相同，且文件结构已正确'
        };
      }

      // 如果是结构迁移（统一存储 -> 分类文件夹）
      if (oldStoragePath === newStoragePath && hasUnifiedStructure && usesCategoryFolders) {
        console.log('🔄 执行存储结构迁移：统一存储 -> 分类文件夹');
      }

      // 获取所有图片记录
      const db = this.dbManager.getDatabase() as Database.Database;
      const allImages = db.prepare('SELECT * FROM images').all() as any[];
      const allCategories = db.prepare('SELECT id, name FROM categories').all() as any[];
      
      console.log(`📊 迁移统计: ${allImages.length} 张图片, ${allCategories.length} 个分类`);

      if (allImages.length === 0) {
        console.log('📭 没有图片需要迁移');
        return {
          success: true,
          message: '没有图片需要迁移'
        };
      }

      // 统计信息
      let successCount = 0;
      let failedCount = 0;
      const failedFiles: string[] = [];
      const migratedFiles: {category: string, files: number}[] = [];

      // 按分类分组迁移
      const categoryMap = new Map();
      allCategories.forEach(cat => categoryMap.set(cat.id, cat.name));

      // 为每个分类创建目标目录
      for (const category of allCategories) {
        const categoryName = this.sanitizeFolderName(category.name);
        const targetImageDir = path.join(newStoragePath, categoryName);
        const targetThumbnailDir = path.join(newStoragePath, categoryName, 'thumbnails');
        
        if (!fs.existsSync(targetImageDir)) {
          fs.mkdirSync(targetImageDir, { recursive: true });
        }
        if (!fs.existsSync(targetThumbnailDir)) {
          fs.mkdirSync(targetThumbnailDir, { recursive: true });
        }
        
        console.log(`📁 创建分类目录: ${categoryName} -> ${targetImageDir}`);
      }

      // 迁移图片文件
      for (const image of allImages) {
        try {
          const categoryName = categoryMap.get(image.category_id);
          if (!categoryName) {
            console.warn(`⚠️ 图片 ${image.id} 的分类 ${image.category_id} 不存在`);
            failedCount++;
            failedFiles.push(`${image.stored_filename} (分类不存在)`);
            continue;
          }

          const sanitizedCategoryName = this.sanitizeFolderName(categoryName);
          
          // 查找旧文件路径
          const oldImagePath = path.join(oldStoragePath, sanitizedCategoryName, image.stored_filename);
          const thumbFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
          const oldThumbnailPath = path.join(oldStoragePath, sanitizedCategoryName, 'thumbnails', thumbFilename);

          // 新文件路径
          const newImagePath = path.join(newStoragePath, sanitizedCategoryName, image.stored_filename);
          const newThumbnailPath = path.join(newStoragePath, sanitizedCategoryName, 'thumbnails', thumbFilename);

          // 移动原图
          if (fs.existsSync(oldImagePath)) {
            fs.copyFileSync(oldImagePath, newImagePath);
            fs.unlinkSync(oldImagePath);
            console.log(`📷 迁移原图: ${image.stored_filename}`);
          } else {
            console.warn(`⚠️ 原图文件不存在: ${oldImagePath}`);
          }

          // 移动缩略图
          if (fs.existsSync(oldThumbnailPath)) {
            fs.copyFileSync(oldThumbnailPath, newThumbnailPath);
            fs.unlinkSync(oldThumbnailPath);
            console.log(`🖼️ 迁移缩略图: ${thumbFilename}`);
          } else {
            console.warn(`⚠️ 缩略图文件不存在: ${oldThumbnailPath}`);
          }

          // 更新数据库中的路径信息
          const newRelativeImagePath = path.join(sanitizedCategoryName, image.stored_filename).replace(/\\/g, '/');
          const newRelativeThumbnailPath = path.join(sanitizedCategoryName, 'thumbnails', thumbFilename).replace(/\\/g, '/');
          
          db.prepare(`
            UPDATE images 
            SET relative_file_path = ?, relative_thumbnail_path = ?, updated_at = ?
            WHERE id = ?
          `).run(newRelativeImagePath, newRelativeThumbnailPath, new Date().toISOString(), image.id);

          successCount++;
          
          // 统计分类迁移情况
          let categoryStats = migratedFiles.find(item => item.category === categoryName);
          if (!categoryStats) {
            categoryStats = { category: categoryName, files: 0 };
            migratedFiles.push(categoryStats);
          }
          categoryStats.files++;
          
        } catch (error) {
          console.error(`❌ 迁移图片 ${image.stored_filename} 失败:`, error);
          failedCount++;
          failedFiles.push(`${image.stored_filename} (${error instanceof Error ? error.message : String(error)})`);
        }
      }

      // 清理旧的空目录
      try {
        for (const category of allCategories) {
          const categoryName = this.sanitizeFolderName(category.name);
          const oldCategoryDir = path.join(oldStoragePath, categoryName);
          const oldThumbnailDir = path.join(oldCategoryDir, 'thumbnails');
          
          // 删除空的缩略图目录
          if (fs.existsSync(oldThumbnailDir) && fs.readdirSync(oldThumbnailDir).length === 0) {
            fs.rmdirSync(oldThumbnailDir);
          }
          
          // 如果分类目录为空，删除它
          if (fs.existsSync(oldCategoryDir) && fs.readdirSync(oldCategoryDir).length === 0) {
            fs.rmdirSync(oldCategoryDir);
            console.log(`🗑️ 清理旧的分类目录: ${categoryName}`);
          }
        }
      } catch (error) {
        console.warn('⚠️ 清理旧目录时出现问题:', error);
      }

      const result = {
        success: failedCount === 0,
        message: `迁移完成: 成功 ${successCount} 张，失败 ${failedCount} 张`,
        details: {
          total: allImages.length,
          success: successCount,
          failed: failedCount,
          failedFiles,
          migratedCategories: migratedFiles
        }
      };

      console.log('✅ 数据迁移完成:', result.message);
      return result;
      
    } catch (error) {
      console.error('❌ 数据迁移失败:', error);
      return {
        success: false,
        message: `迁移失败: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  /**
   * 获取OSS服务实例（用于外部访问）
   */
  getOSSService(): OSSService {
    return this.ossService;
  }

  /**
   * 重新初始化OSS服务（当配置更改时调用）
   */
  reinitializeOSSService(): void {
    this.initializeOSSService();
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    if (this.ossService) {
      this.ossService.destroy();
    }
  }
}