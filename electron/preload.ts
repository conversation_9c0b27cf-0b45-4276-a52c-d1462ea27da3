import { contextBridge, ipc<PERSON>enderer } from 'electron';

contextBridge.exposeInMainWorld('electronAPI', {
  isElectron: true,
  platform: process.platform,
  
  // 数据库相关方法
  testDatabase: () => ipcRenderer.invoke('test-database'),
  getDatabaseStats: () => ipcRenderer.invoke('get-database-stats'),
  resetDatabase: () => ipcRenderer.invoke('reset-database'),
  
  // 分类相关方法
  getCategories: (skip?: number, limit?: number) => 
    ipcRenderer.invoke('get-categories', skip, limit),
  createCategory: (categoryData: any) => 
    ipcRenderer.invoke('create-category', categoryData),
  updateCategory: (categoryId: string, categoryData: any) => 
    ipcRenderer.invoke('update-category', categoryId, categoryData),
  deleteCategory: (categoryId: string) => 
    ipcRenderer.invoke('delete-category', categoryId),
  getCategoryById: (categoryId: string) => 
    ipcRenderer.invoke('get-category-by-id', categoryId),
  getCategoryWithImages: (categoryId: string) => 
    ipcRenderer.invoke('get-category-with-images', categoryId),
  
  // 图片相关方法
  uploadImage: (categoryId: string, fileBuffer: any, originalFilename: string, mimeType: string, setAsCategoryThumbnail?: boolean) => 
    ipcRenderer.invoke('upload-image', categoryId, fileBuffer, originalFilename, mimeType, setAsCategoryThumbnail),
  getImageById: (imageId: string) => 
    ipcRenderer.invoke('get-image-by-id', imageId),
  updateImage: (imageId: string, imageData: any) => 
    ipcRenderer.invoke('update-image', imageId, imageData),
  deleteImage: (imageId: string) => 
    ipcRenderer.invoke('delete-image', imageId),
  getImagePath: (filename: string) => 
    ipcRenderer.invoke('get-image-path', filename),
  getThumbnailPath: (filename: string) => 
    ipcRenderer.invoke('get-thumbnail-path', filename),
  
  // 标签相关方法
  getAllTags: () => 
    ipcRenderer.invoke('get-all-tags'),
  createTag: (tagData: any) => 
    ipcRenderer.invoke('create-tag', tagData),
  updateTag: (tagId: string, tagData: any) => 
    ipcRenderer.invoke('update-tag', tagId, tagData),
  deleteTag: (tagId: string) => 
    ipcRenderer.invoke('delete-tag', tagId),
  getTagById: (tagId: string) => 
    ipcRenderer.invoke('get-tag-by-id', tagId),
  searchTags: (query: string) => 
    ipcRenderer.invoke('search-tags', query),
  addTagToImage: (imageId: string, tagId: string) => 
    ipcRenderer.invoke('add-tag-to-image', imageId, tagId),
  removeTagFromImage: (imageId: string, tagId: string) => 
    ipcRenderer.invoke('remove-tag-from-image', imageId, tagId),
  getTagsForImage: (imageId: string) => 
    ipcRenderer.invoke('get-tags-for-image', imageId),
  searchImagesByTags: (tagNames: string[]) => 
    ipcRenderer.invoke('search-images-by-tags', tagNames),

  // 存储配置相关方法
  getStorageSettings: () => 
    ipcRenderer.invoke('get-storage-settings'),
  updateStorageSettings: (settings: any) => 
    ipcRenderer.invoke('update-storage-settings', settings),
  selectDirectory: () => 
    ipcRenderer.invoke('select-directory'),
  migrateStorageLocation: (newPath: string) => 
    ipcRenderer.invoke('migrate-storage-location', newPath),

  // OSS配置相关方法
  getOSSConfig: () => 
    ipcRenderer.invoke('get-oss-config'),
  updateOSSConfig: (config: any) => 
    ipcRenderer.invoke('update-oss-config', config),
  testOSSConnection: (config: any) => 
    ipcRenderer.invoke('test-oss-connection', config),
  switchStorageType: (storageType: 'local' | 'oss') => 
    ipcRenderer.invoke('switch-storage-type', storageType),

  // 菜单事件监听
  onMenuAction: (callback: (action: string, data?: any) => void) => {
    ipcRenderer.on('menu-action', (_, action, data) => callback(action, data));
  },
  
  // 应用事件监听
  onAppReady: (callback: () => void) => {
    ipcRenderer.on('app-ready', callback);
  },
  
  // 移除事件监听器
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  }
});