<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>OSS配置</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }
        
        .required {
            color: #e74c3c;
        }
        
        .optional {
            color: #888;
            font-size: 12px;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #45a049;
        }
        
        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #1976D2;
        }
        
        .btn-cancel {
            background-color: #f44336;
            color: white;
        }
        
        .btn-cancel:hover {
            background-color: #d32f2f;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>存储配置</h1>
        
        <!-- 存储类型选择 -->
        <div class="form-group">
            <label>存储类型 <span class="required">*</span></label>
            <div style="display: flex; gap: 20px; margin-top: 10px;">
                <label style="display: flex; align-items: center; font-weight: normal;">
                    <input type="radio" name="storageType" value="local" id="localStorage" style="margin-right: 8px;">
                    本地存储
                </label>
                <label style="display: flex; align-items: center; font-weight: normal;">
                    <input type="radio" name="storageType" value="oss" id="ossStorage" style="margin-right: 8px;">
                    OSS存储
                </label>
            </div>
            <div class="help-text">选择图片存储方式</div>
        </div>

        <!-- OSS配置区域 -->
        <div id="ossConfigSection" style="display: none;">
            <h2 style="color: #666; font-size: 18px; margin: 30px 0 20px 0;">OSS配置</h2>
        
            <div class="form-group">
                <label for="endpoint">服务端点 <span class="required">*</span></label>
                <input type="text" id="endpoint" name="endpoint" placeholder="https://oss-cn-hangzhou.aliyuncs.com" required>
                <div class="help-text">OSS服务的端点地址，例如：https://oss-cn-hangzhou.aliyuncs.com</div>
            </div>
            
            <div class="form-group">
                <label for="region">区域 <span class="required">*</span></label>
                <input type="text" id="region" name="region" placeholder="cn-hangzhou" required>
                <div class="help-text">OSS服务的区域，例如：cn-hangzhou</div>
            </div>
            
            <div class="form-group">
                <label for="accessKeyId">访问密钥ID <span class="required">*</span></label>
                <input type="text" id="accessKeyId" name="accessKeyId" placeholder="LTAI..." required>
                <div class="help-text">阿里云Access Key ID</div>
            </div>
            
            <div class="form-group">
                <label for="secretAccessKey">访问密钥Secret <span class="required">*</span></label>
                <input type="password" id="secretAccessKey" name="secretAccessKey" placeholder="密钥..." required>
                <div class="help-text">阿里云Access Key Secret</div>
            </div>
            
            <div class="form-group">
                <label for="bucket">存储桶名称 <span class="required">*</span></label>
                <input type="text" id="bucket" name="bucket" placeholder="my-bucket" required>
                <div class="help-text">OSS存储桶名称</div>
            </div>
            
            <div class="form-group">
                <label for="pathPrefix">路径前缀 <span class="optional">(可选)</span></label>
                <input type="text" id="pathPrefix" name="pathPrefix" placeholder="pokedex">
                <div class="help-text">文件存储的路径前缀，可以为空</div>
            </div>
        </div>

        <!-- 按钮区域 -->
        <div class="buttons">
            <button type="button" id="testBtn" class="btn-secondary" style="display: none;">测试连接</button>
            <button type="button" id="saveStorageBtn" class="btn-primary">保存设置</button>
            <button type="button" id="cancelBtn" class="btn-cancel">取消</button>
        </div>
            
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        // 加载当前配置
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const result = await ipcRenderer.invoke('get-oss-config');
                if (result.success) {
                    // 设置存储类型
                    const storageType = result.storageType || 'local';
                    if (storageType === 'local') {
                        document.getElementById('localStorage').checked = true;
                    } else {
                        document.getElementById('ossStorage').checked = true;
                    }
                    
                    // 根据存储类型显示/隐藏OSS配置区域
                    toggleOSSConfigSection();
                    
                    // 加载OSS配置
                    if (result.config) {
                        const config = result.config;
                        document.getElementById('endpoint').value = config.endpoint || '';
                        document.getElementById('region').value = config.region || '';
                        document.getElementById('accessKeyId').value = config.accessKeyId || '';
                        document.getElementById('secretAccessKey').value = config.secretAccessKey || '';
                        document.getElementById('bucket').value = config.bucket || '';
                        document.getElementById('pathPrefix').value = config.pathPrefix || '';
                    }
                }
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        });
        
        // 切换OSS配置区域显示/隐藏
        function toggleOSSConfigSection() {
            const ossStorage = document.getElementById('ossStorage');
            const ossConfigSection = document.getElementById('ossConfigSection');
            const testBtn = document.getElementById('testBtn');
            const saveBtn = document.getElementById('saveStorageBtn');
            
            if (ossStorage.checked) {
                ossConfigSection.style.display = 'block';
                testBtn.style.display = 'inline-block';
                saveBtn.textContent = '保存OSS设置';
            } else {
                ossConfigSection.style.display = 'none';
                testBtn.style.display = 'none';
                saveBtn.textContent = '保存设置';
            }
        }
        
        // 监听存储类型变化
        document.getElementById('localStorage').addEventListener('change', toggleOSSConfigSection);
        document.getElementById('ossStorage').addEventListener('change', toggleOSSConfigSection);
        
        // 显示状态信息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        // 获取表单数据
        function getFormData() {
            return {
                endpoint: document.getElementById('endpoint').value.trim(),
                region: document.getElementById('region').value.trim(),
                accessKeyId: document.getElementById('accessKeyId').value.trim(),
                secretAccessKey: document.getElementById('secretAccessKey').value.trim(),
                bucket: document.getElementById('bucket').value.trim(),
                pathPrefix: document.getElementById('pathPrefix').value.trim()
            };
        }
        
        // 测试连接
        document.getElementById('testBtn').addEventListener('click', async () => {
            const config = getFormData();
            
            // 验证必填字段
            if (!config.endpoint || !config.region || !config.accessKeyId || !config.secretAccessKey || !config.bucket) {
                showStatus('请填写所有必填字段', 'error');
                return;
            }
            
            showStatus('正在测试连接...', 'info');
            
            try {
                const result = await ipcRenderer.invoke('test-oss-connection', config);
                if (result.success) {
                    showStatus('连接测试成功！', 'success');
                } else {
                    showStatus(`连接测试失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`连接测试失败: ${error.message}`, 'error');
            }
        });
        
        // 保存设置
        document.getElementById('saveStorageBtn').addEventListener('click', async () => {
            const storageType = document.getElementById('localStorage').checked ? 'local' : 'oss';
            
            // 如果选择OSS存储，需要验证OSS配置
            if (storageType === 'oss') {
                const config = getFormData();
                
                // 验证必填字段
                if (!config.endpoint || !config.region || !config.accessKeyId || !config.secretAccessKey || !config.bucket) {
                    showStatus('使用OSS存储需要填写所有必填字段', 'error');
                    return;
                }
                
                showStatus('正在保存OSS配置...', 'info');
                
                try {
                    // 先保存OSS配置
                    const ossResult = await ipcRenderer.invoke('update-oss-config', config);
                    if (!ossResult.success) {
                        showStatus(`保存OSS配置失败: ${ossResult.message}`, 'error');
                        return;
                    }
                } catch (error) {
                    showStatus(`保存OSS配置失败: ${error.message}`, 'error');
                    return;
                }
            }
            
            // 切换存储类型
            const statusMessage = storageType === 'local' ? '正在切换到本地存储...' : '正在切换到OSS存储...';
            showStatus(statusMessage, 'info');
            
            try {
                const result = await ipcRenderer.invoke('switch-storage-type', storageType);
                if (result.success) {
                    const successMessage = storageType === 'local' ? '已切换到本地存储！' : '已切换到OSS存储！';
                    showStatus(successMessage, 'success');
                    
                    // 刷新主窗口
                    try {
                        showStatus('正在刷新主窗口...', 'info');
                        const refreshResult = await ipcRenderer.invoke('refresh-main-window');
                        if (refreshResult.success) {
                            showStatus('配置已保存，主窗口已刷新！', 'success');
                        } else {
                            console.warn('刷新主窗口失败:', refreshResult.message);
                            showStatus('配置已保存！', 'success');
                        }
                    } catch (refreshError) {
                        console.warn('刷新主窗口失败:', refreshError);
                        showStatus('配置已保存！', 'success');
                    }
                    
                    setTimeout(() => {
                        window.close();
                    }, 1500);
                } else {
                    showStatus(`切换存储类型失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`切换存储类型失败: ${error.message}`, 'error');
            }
        });
        
        // 取消
        document.getElementById('cancelBtn').addEventListener('click', () => {
            window.close();
        });
    </script>
</body>
</html>