# netlify.toml

# 构建配置
[build]
  publish = "dist"
  command = "npm run build"

# 代理 API 请求
[[redirects]]
  from = "/api/*"
  to = "http://39.107.88.124:8000/api/:splat"
  status = 200

# 代理 thumbnails 图片
[[redirects]]
  from = "/thumbnails/*"
  to = "http://39.107.88.124:8000/thumbnails/:splat"
  status = 200

# 代理 uploaded_images 图片
[[redirects]]
  from = "/uploaded_images/*"
  to = "http://39.107.88.124:8000/uploaded_images/:splat"
  status = 200

# SPA 回退路由
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
