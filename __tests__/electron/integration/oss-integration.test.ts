import { DatabaseManager } from '../../../electron/database';
import { SettingsService } from '../../../electron/services/SettingsService';
import { ImageService } from '../../../electron/services/ImageService';
import { OSSService } from '../../../electron/services/OSSService';
import { createTestDatabase, cleanupTestDatabase } from '../helpers/test-database';
import { createTestImage } from '../helpers/test-data-generator';
import fs from 'fs';
import path from 'path';

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock AWS SDK to avoid actual network calls
vi.mock('@aws-sdk/client-s3');
vi.mock('@aws-sdk/lib-storage');
vi.mock('@aws-sdk/s3-request-presigner');

describe('OSS Integration Tests', () => {
  let dbManager: DatabaseManager;
  let settingsService: SettingsService;
  let imageService: ImageService;
  let ossService: OSSService;

  beforeEach(async () => {
    // Setup test database
    const testDb = createTestDatabase();
    dbManager = {
      getDatabase: () => testDb.db,
      testConnection: vi.fn().mockResolvedValue({ success: true }),
      getStats: vi.fn().mockResolvedValue({ success: true }),
      close: vi.fn().mockResolvedValue(undefined)
    } as any;

    // Mock electron app for isolated settings
    vi.doMock('electron', () => ({
      app: {
        getPath: vi.fn().mockReturnValue(`/tmp/test-${Date.now()}`)
      }
    }));

    // Setup services with fresh instances
    settingsService = new SettingsService();
    // Ensure category folders are enabled
    settingsService.saveSettings({
      usesCategoryFolders: true,
      storagePath: settingsService.getStoragePath()
    });
    imageService = new ImageService(dbManager, settingsService);
    ossService = new OSSService();
  });

  afterEach(() => {
    if (dbManager && dbManager.close) {
      dbManager.close();
    }
    cleanupTestDatabase();
  });

  describe('Settings Service OSS Integration', () => {
    it('should store and retrieve OSS configuration', () => {
      const ossConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      };

      // Save OSS config
      const saveResult = settingsService.setOSSConfig(ossConfig);
      expect(saveResult).toBe(true);

      // Retrieve OSS config
      const retrievedConfig = settingsService.getOSSConfig();
      expect(retrievedConfig).toEqual(ossConfig);

      // Check configuration status
      expect(settingsService.isOSSConfigured()).toBe(true);
    });

    it('should validate OSS configuration', () => {
      const validConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket'
      };

      const invalidConfig = {
        endpoint: '',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: '',
        bucket: 'test-bucket'
      };

      expect(settingsService.validateOSSConfig(validConfig)).toBe(true);
      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
    });

    it('should switch storage types', () => {
      // Default should be local
      expect(settingsService.getStorageType()).toBe('local');

      // Switch to OSS
      const switchResult = settingsService.setStorageType('oss');
      expect(switchResult).toBe(true);
      expect(settingsService.getStorageType()).toBe('oss');

      // Switch back to local
      const switchBackResult = settingsService.setStorageType('local');
      expect(switchBackResult).toBe(true);
      expect(settingsService.getStorageType()).toBe('local');
    });
  });

  describe('Image Service OSS Integration', () => {
    beforeEach(() => {
      // Setup OSS configuration
      const ossConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      };

      settingsService.setOSSConfig(ossConfig);
    });

    it('should detect storage type correctly', () => {
      // Test local storage type
      settingsService.setStorageType('local');
      expect(imageService['getStorageType']()).toBe('local');

      // Test OSS storage type
      settingsService.setStorageType('oss');
      expect(imageService['getStorageType']()).toBe('oss');
    });

    it('should generate OSS paths correctly', () => {
      // Create a test category first
      const db = dbManager.getDatabase();
      const insertCategory = db.prepare('INSERT INTO categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?)');
      insertCategory.run('test-category-id', 'Test Category', 'Test description', new Date().toISOString(), new Date().toISOString());

      // Test OSS path generation
      const imagePath = imageService['generateOSSPath']('test-category-id', 'test-image.jpg', false);
      const thumbnailPath = imageService['generateOSSPath']('test-category-id', 'test-image.jpg', true);

      expect(imagePath).toBe('Test_Category/images/test-image.jpg');
      expect(thumbnailPath).toBe('Test_Category/thumbnails/test-image.jpg');
    });

    it('should handle path resolution for different storage types', () => {
      // Create a test category and image
      const db = dbManager.getDatabase();
      const insertCategory = db.prepare('INSERT INTO categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?)');
      insertCategory.run('test-category-id', 'Test Category', 'Test description', new Date().toISOString(), new Date().toISOString());

      const insertImage = db.prepare(`
        INSERT INTO images (id, category_id, title, stored_filename, relative_file_path, relative_thumbnail_path, image_url, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        'test-image-id',
        'test-category-id',
        'Test Image',
        'test-image.jpg',
        'Test_Category/images/test-image.jpg',
        'Test_Category/thumbnails/test-image.jpg',
        'electron://file/test-image.jpg',
        'electron://thumbnail/test-image_thumb.jpg',
        new Date().toISOString(),
        new Date().toISOString()
      );

      // Test local storage path
      settingsService.setStorageType('local');
      const localPath = imageService.getImagePath('test-image.jpg');
      expect(localPath).toContain('Test_Category');
      expect(localPath).toContain('test-image.jpg');

      // Test OSS storage path
      settingsService.setStorageType('oss');
      const ossPath = imageService.getImagePath('test-image.jpg');
      expect(ossPath).toBe('Test_Category/images/test-image.jpg');
    });
  });

  describe('OSS Service Configuration', () => {
    it('should initialize OSS service with configuration', () => {
      const ossConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      };

      ossService.updateConfig(ossConfig);

      expect(ossService.isConfigured()).toBe(true);
      expect(ossService.getConfig()).toEqual(ossConfig);
    });

    it('should handle configuration updates', () => {
      const initialConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      };

      const updatedConfig = {
        ...initialConfig,
        bucket: 'new-bucket',
        pathPrefix: 'new-prefix'
      };

      // Initial configuration
      ossService.updateConfig(initialConfig);
      expect(ossService.getConfig()).toEqual(initialConfig);

      // Update configuration
      ossService.updateConfig(updatedConfig);
      expect(ossService.getConfig()).toEqual(updatedConfig);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing OSS configuration gracefully', async () => {
      // Reset modules to ensure fresh imports
      vi.resetModules();
      
      // Mock a new temporary directory for this test
      vi.doMock('electron', () => ({
        app: {
          getPath: vi.fn().mockReturnValue(`/tmp/test-no-oss-${Date.now()}`)
        }
      }));
      
      // Import SettingsService after mocking
      const { SettingsService: FreshSettingsService } = await import('../../../electron/services/SettingsService');
      const freshSettingsService = new FreshSettingsService();
      
      // Test without OSS configuration
      expect(freshSettingsService.isOSSConfigured()).toBe(false);
      expect(freshSettingsService.getOSSConfig()).toBeUndefined();

      // Test storage type validation
      freshSettingsService.setStorageType('oss');
      const settings = freshSettingsService.getSettings();
      // Should fall back to local storage due to missing OSS config
      expect(settings.storageType).toBe('local');
    });

    it('should handle invalid OSS configuration', () => {
      const invalidConfig = {
        endpoint: '',
        region: '',
        accessKeyId: '',
        secretAccessKey: '',
        bucket: ''
      };

      expect(settingsService.validateOSSConfig(invalidConfig)).toBe(false);
      
      // Try to save invalid config
      const saveResult = settingsService.setOSSConfig(invalidConfig);
      expect(saveResult).toBe(true); // Saves but validation will fail

      // Check that isOSSConfigured returns false for invalid config
      expect(settingsService.isOSSConfigured()).toBe(false);
    });
  });

  describe('Data Consistency', () => {
    it('should maintain data consistency when switching storage types', () => {
      // Create test data
      const db = dbManager.getDatabase();
      const insertCategory = db.prepare('INSERT INTO categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?)');
      insertCategory.run('test-category-id', 'Test Category', 'Test description', new Date().toISOString(), new Date().toISOString());

      // Test with local storage
      settingsService.setStorageType('local');
      expect(settingsService.getStorageType()).toBe('local');

      // Configure OSS
      const ossConfig = {
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      };
      settingsService.setOSSConfig(ossConfig);

      // Switch to OSS
      settingsService.setStorageType('oss');
      expect(settingsService.getStorageType()).toBe('oss');
      expect(settingsService.isOSSConfigured()).toBe(true);

      // Switch back to local
      settingsService.setStorageType('local');
      expect(settingsService.getStorageType()).toBe('local');
      expect(settingsService.isOSSConfigured()).toBe(true); // OSS config should persist
    });
  });

  describe('Path Sanitization', () => {
    it('should sanitize folder names for OSS paths', () => {
      // Create categories with special characters
      const db = dbManager.getDatabase();
      const insertCategory = db.prepare('INSERT INTO categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, ?, ?)');
      
      // Test various special characters
      const testCases = [
        { name: 'Category with spaces', expected: 'Category_with_spaces' },
        { name: 'Category/with\\slashes', expected: 'Category_with_slashes' },
        { name: 'Category<with>brackets', expected: 'Category_with_brackets' },
        { name: 'Category:with|special*chars', expected: 'Category_with_special_chars' },
        { name: '   Multiple   Spaces   ', expected: 'Multiple_Spaces' }
      ];

      testCases.forEach(({ name, expected }, index) => {
        const categoryId = `test-category-${index}`;
        insertCategory.run(categoryId, name, 'Test description', new Date().toISOString(), new Date().toISOString());

        const sanitizedName = imageService['getCategoryName'](categoryId);
        expect(sanitizedName).toBe(expected);
      });
    });
  });
});