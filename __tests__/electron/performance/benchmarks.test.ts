import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { CategoryService } from '../../../electron/services/CategoryService';
import { ImageService } from '../../../electron/services/ImageService';
import { TagService } from '../../../electron/services/TagService';
import { SettingsService } from '../../../electron/services/SettingsService';

interface PerformanceMetrics {
  operationTime: number;
  memoryBefore: number;
  memoryAfter: number;
  peakMemory: number;
  operationsPerSecond: number;
}

describe('Performance Benchmark Tests', () => {
  let dbManager: DatabaseManager;
  let categoryService: CategoryService;
  let imageService: ImageService;
  let tagService: TagService;
  let settingsService: SettingsService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 使用性能测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建真实的DatabaseManager实例
    dbManager = {
      getDatabase: () => testDb,
      testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
      getStats: vi.fn(() => ({ success: true, message: '统计成功', mode: 'SQLite', data: {} })),
      close: vi.fn()
    } as any;

    // 创建真实的服务实例
    categoryService = new CategoryService(dbManager);
    tagService = new TagService(dbManager);
    settingsService = new SettingsService();
    imageService = new ImageService(dbManager, settingsService);

    // 静默控制台输出
    console.log = vi.fn();
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  // 性能测量工具函数
  function measurePerformance<T>(operation: () => Promise<T>): Promise<{ result: T; metrics: PerformanceMetrics }> {
    return new Promise(async (resolve) => {
      const memoryBefore = process.memoryUsage().heapUsed;
      let peakMemory = memoryBefore;
      
      // 监控内存峰值
      const memoryMonitor = setInterval(() => {
        const currentMemory = process.memoryUsage().heapUsed;
        if (currentMemory > peakMemory) {
          peakMemory = currentMemory;
        }
      }, 10);

      const startTime = performance.now();
      const result = await operation();
      const endTime = performance.now();
      
      clearInterval(memoryMonitor);
      
      const memoryAfter = process.memoryUsage().heapUsed;
      const operationTime = endTime - startTime;
      
      const metrics: PerformanceMetrics = {
        operationTime,
        memoryBefore,
        memoryAfter,
        peakMemory,
        operationsPerSecond: 1000 / operationTime
      };

      resolve({ result, metrics });
    });
  }

  describe('数据库操作性能基准', () => {
    it('应该在合理时间内完成大量分类创建操作', async () => {
      const categoryCount = 1000;
      const categories: any[] = [];

      const { result, metrics } = await measurePerformance(async () => {
        for (let i = 0; i < categoryCount; i++) {
          const category = await categoryService.createCategory({
            name: `Performance Category ${i}`,
            description: `Performance test description ${i}`
          });
          categories.push(category);
        }
        return categories;
      });

      // 性能断言
      expect(result).toHaveLength(categoryCount);
      expect(metrics.operationTime).toBeLessThan(30000); // 30秒内完成
      expect(metrics.operationsPerSecond).toBeGreaterThan(30); // 至少每秒30个操作
      expect(metrics.peakMemory - metrics.memoryBefore).toBeLessThan(50 * 1024 * 1024); // 内存增长不超过50MB

      console.log(`分类创建性能: ${categoryCount}个分类 耗时${Math.round(metrics.operationTime)}ms, ` +
                  `速率${Math.round(metrics.operationsPerSecond)}/sec, ` +
                  `内存增长${Math.round((metrics.peakMemory - metrics.memoryBefore) / 1024 / 1024)}MB`);
    });

    it('应该高效执行分页查询操作', async () => {
      // 先创建一些测试数据
      const setupCategories = [];
      for (let i = 0; i < 100; i++) {
        const category = await categoryService.createCategory({
          name: `Query Test Category ${i}`,
          description: `Query test ${i}`
        });
        setupCategories.push(category);
      }

      // 测试多个分页查询的性能
      const { result, metrics } = await measurePerformance(async () => {
        const queryResults = [];
        
        // 测试不同分页参数的查询
        for (let page = 0; page < 10; page++) {
          const categories = await categoryService.getCategories(page * 10, 10);
          queryResults.push(categories);
        }
        
        // 测试大页面查询
        const largePage = await categoryService.getCategories(0, 50);
        queryResults.push(largePage);
        
        return queryResults;
      });

      // 性能断言
      expect(result).toHaveLength(11); // 10个小页面 + 1个大页面
      expect(metrics.operationTime).toBeLessThan(1000); // 1秒内完成
      expect(metrics.operationsPerSecond).toBeGreaterThan(1); // 至少每秒1次完整测试

      console.log(`分页查询性能: 11次查询 耗时${Math.round(metrics.operationTime)}ms, ` +
                  `平均${Math.round(metrics.operationTime / 11)}ms/查询`);
    });

    it('应该高效处理复杂的标签搜索操作', async () => {
      // 创建测试数据
      const category = await categoryService.createCategory({ name: 'Search Test Category' });
      
      // 创建大量标签
      const tags = [];
      for (let i = 0; i < 500; i++) {
        const tag = await tagService.createTag({ name: `SearchTag${i}` });
        tags.push(tag);
      }

      // 创建图片并关联标签
      const images = [];
      for (let i = 0; i < 100; i++) {
        const image = await imageService.uploadImage(
          category.id,
          Buffer.from(`search test image ${i}`),
          `search-${i}.jpg`,
          'image/jpeg'
        );
        images.push(image);

        // 每个图片关联5-10个随机标签
        const tagCount = 5 + Math.floor(Math.random() * 6);
        for (let j = 0; j < tagCount; j++) {
          const randomTag = tags[Math.floor(Math.random() * tags.length)];
          await tagService.addTagToImage(image.id, randomTag.id);
        }
      }

      // 测试搜索性能
      const { result, metrics } = await measurePerformance(async () => {
        const searchResults = [];
        
        // 测试不同类型的搜索
        const singleTagSearch = await tagService.searchImagesByTags(['SearchTag1']);
        searchResults.push(singleTagSearch);
        
        const multiTagSearch = await tagService.searchImagesByTags(['SearchTag1', 'SearchTag2', 'SearchTag3']);
        searchResults.push(multiTagSearch);
        
        const tagNameSearch = await tagService.searchTags('SearchTag');
        searchResults.push(tagNameSearch);
        
        const partialSearch = await tagService.searchTags('Tag1');
        searchResults.push(partialSearch);
        
        return searchResults;
      });

      // 性能断言
      expect(result).toHaveLength(4);
      expect(metrics.operationTime).toBeLessThan(5000); // 5秒内完成
      expect(metrics.operationsPerSecond).toBeGreaterThan(0.2); // 至少每5秒1次完整测试

      console.log(`标签搜索性能: 4次搜索 耗时${Math.round(metrics.operationTime)}ms, ` +
                  `平均${Math.round(metrics.operationTime / 4)}ms/搜索`);
    });
  });

  describe('图片处理性能测试', () => {
    it('应该高效处理大量图片上传', async () => {
      const category = await categoryService.createCategory({ name: 'Upload Performance Test' });
      const imageCount = 50;

      const { result, metrics } = await measurePerformance(async () => {
        const uploadPromises = [];
        
        for (let i = 0; i < imageCount; i++) {
          const imageData = Buffer.alloc(1024 * 100, `image-${i}`); // 100KB 测试图片
          const promise = imageService.uploadImage(
            category.id,
            imageData,
            `perf-test-${i}.jpg`,
            'image/jpeg'
          );
          uploadPromises.push(promise);
        }
        
        return Promise.all(uploadPromises);
      });

      // 性能断言
      expect(result).toHaveLength(imageCount);
      expect(metrics.operationTime).toBeLessThan(20000); // 20秒内完成
      expect(metrics.operationsPerSecond).toBeGreaterThan(2); // 至少每秒2个图片
      
      console.log(`图片上传性能: ${imageCount}个图片 耗时${Math.round(metrics.operationTime)}ms, ` +
                  `速率${Math.round(imageCount * 1000 / metrics.operationTime)}/sec`);
    });

    it('应该高效处理图片删除操作', async () => {
      const category = await categoryService.createCategory({ name: 'Delete Performance Test' });
      
      // 先创建测试图片
      const images = [];
      for (let i = 0; i < 100; i++) {
        const image = await imageService.uploadImage(
          category.id,
          Buffer.from(`delete test ${i}`),
          `delete-test-${i}.jpg`,
          'image/jpeg'
        );
        images.push(image);
      }

      // 测试删除性能
      const { result, metrics } = await measurePerformance(async () => {
        const deletePromises = images.map(image => 
          imageService.deleteImage(image.id)
        );
        return Promise.all(deletePromises);
      });

      // 性能断言
      expect(result).toHaveLength(images.length);
      expect(metrics.operationTime).toBeLessThan(10000); // 10秒内完成
      expect(metrics.operationsPerSecond).toBeGreaterThan(0.1); // 至少每10秒1次完整删除

      console.log(`图片删除性能: ${images.length}个图片 耗时${Math.round(metrics.operationTime)}ms, ` +
                  `速率${Math.round(images.length * 1000 / metrics.operationTime)}/sec`);
    });
  });

  describe('内存使用情况监控', () => {
    it('应该在大量操作后保持合理的内存使用', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行大量操作
      const category = await categoryService.createCategory({ name: 'Memory Test Category' });
      
      const { result, metrics } = await measurePerformance(async () => {
        const operations = [];
        
        // 创建数据
        for (let i = 0; i < 200; i++) {
          operations.push(
            categoryService.createCategory({ name: `Memory Cat ${i}` })
          );
        }
        
        // 上传图片
        for (let i = 0; i < 50; i++) {
          operations.push(
            imageService.uploadImage(
              category.id,
              Buffer.alloc(1024 * 50, `mem-test-${i}`),
              `mem-test-${i}.jpg`,
              'image/jpeg'
            )
          );
        }
        
        // 创建标签
        for (let i = 0; i < 100; i++) {
          operations.push(
            tagService.createTag({ name: `MemTag${i}` })
          );
        }
        
        return Promise.all(operations);
      });

      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      
      // 内存使用断言
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const peakIncrease = metrics.peakMemory - initialMemory.heapUsed;
      
      expect(result).toHaveLength(350); // 200 + 50 + 100
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 内存增长不超过100MB
      expect(peakIncrease).toBeLessThan(200 * 1024 * 1024); // 峰值内存不超过200MB
      
      console.log(`内存使用监控: 初始${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB, ` +
                  `峰值${Math.round(metrics.peakMemory / 1024 / 1024)}MB, ` +
                  `最终${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB, ` +
                  `增长${Math.round(memoryIncrease / 1024 / 1024)}MB`);
    });

    it('应该高效释放已删除数据的内存', async () => {
      const category = await categoryService.createCategory({ name: 'Memory Cleanup Test' });
      
      // 创建大量数据
      const images = [];
      for (let i = 0; i < 100; i++) {
        const image = await imageService.uploadImage(
          category.id,
          Buffer.alloc(1024 * 100, `cleanup-${i}`), // 100KB
          `cleanup-${i}.jpg`,
          'image/jpeg'
        );
        images.push(image);
      }

      const beforeCleanup = process.memoryUsage();

      // 删除所有数据
      const { result, metrics } = await measurePerformance(async () => {
        for (const image of images) {
          await imageService.deleteImage(image.id);
        }
        await categoryService.deleteCategory(category.id);
        return true;
      });

      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }

      // 等待一小段时间让GC完成
      await new Promise(resolve => setTimeout(resolve, 100));

      const afterCleanup = process.memoryUsage();
      
      expect(result).toBe(true);
      expect(metrics.operationTime).toBeLessThan(10000); // 10秒内完成清理
      
      // 内存应该被释放（允许一些偏差）
      const memoryReleased = beforeCleanup.heapUsed - afterCleanup.heapUsed;
      expect(memoryReleased).toBeGreaterThan(-50 * 1024 * 1024); // 允许50MB偏差
      
      console.log(`内存清理测试: 清理前${Math.round(beforeCleanup.heapUsed / 1024 / 1024)}MB, ` +
                  `清理后${Math.round(afterCleanup.heapUsed / 1024 / 1024)}MB, ` +
                  `释放${Math.round(memoryReleased / 1024 / 1024)}MB`);
    });
  });

  describe('启动时间性能测试', () => {
    it('应该快速初始化服务实例', async () => {
      const { result, metrics } = await measurePerformance(async () => {
        // 测试服务初始化时间
        const testDbManager = TestDatabaseManager.getInstance();
        const testDatabase = testDbManager.createTestDatabase();
        
        const newDbManager = {
          getDatabase: () => testDatabase.db,
          testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
          getStats: vi.fn(() => ({ success: true, message: '统计成功', mode: 'SQLite', data: {} })),
          close: vi.fn()
        } as any;

        const newCategoryService = new CategoryService(newDbManager);
        const newTagService = new TagService(newDbManager);
        const newSettingsService = new SettingsService();
        const newImageService = new ImageService(newDbManager, newSettingsService);

        // 测试首次数据库操作
        await newCategoryService.getCategories(0, 1);
        await newTagService.getAllTags();
        
        testDatabase.cleanup();
        
        return {
          categoryService: newCategoryService,
          imageService: newImageService,
          tagService: newTagService,
          settingsService: newSettingsService
        };
      });

      // 启动性能断言
      expect(result.categoryService).toBeDefined();
      expect(result.imageService).toBeDefined();
      expect(result.tagService).toBeDefined();
      expect(result.settingsService).toBeDefined();
      expect(metrics.operationTime).toBeLessThan(1000); // 1秒内完成初始化
      
      console.log(`服务初始化性能: 耗时${Math.round(metrics.operationTime)}ms`);
    });

    it('应该快速完成数据库连接测试', async () => {
      const { result, metrics } = await measurePerformance(async () => {
        const connectionResults = [];
        
        // 测试多次连接
        for (let i = 0; i < 10; i++) {
          const testDbManager = TestDatabaseManager.getInstance();
          const testDatabase = testDbManager.createTestDatabase();
          
          const dbManager = {
            getDatabase: () => testDatabase.db,
            testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
            getStats: vi.fn(() => ({ success: true, message: '统计成功', mode: 'SQLite', data: {} })),
            close: vi.fn()
          } as any;

          const connectionResult = dbManager.testConnection();
          connectionResults.push(connectionResult);
          
          testDatabase.cleanup();
        }
        
        return connectionResults;
      });

      // 连接性能断言
      expect(result).toHaveLength(10);
      expect(result.every(r => r.success)).toBe(true);
      expect(metrics.operationTime).toBeLessThan(2000); // 2秒内完成
      expect(metrics.operationsPerSecond).toBeGreaterThan(5); // 至少每秒5次连接
      
      console.log(`数据库连接性能: 10次连接 耗时${Math.round(metrics.operationTime)}ms, ` +
                  `平均${Math.round(metrics.operationTime / 10)}ms/连接`);
    });
  });
});