import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ImageService } from '../../../electron/services/ImageService';
import { DatabaseManager } from '../../../electron/database';
import { SettingsService } from '../../../electron/services/SettingsService';
import { TestDataGenerator } from '../helpers/test-data-generator';
import { createTestDatabase, cleanupTestDatabase, mockDatabaseManager } from '../helpers/test-database';
import Database from 'better-sqlite3';
import * as fs from 'fs';
import * as path from 'path';

// Mock Electron modules
vi.mock('electron', async () => {
  const { createElectronMocks } = await import('../helpers/electron-mocks');
  return createElectronMocks();
});

// Mock fs operations for testing
vi.mock('fs');
const mockFs = vi.mocked(fs);

describe('删除功能性能测试', () => {
  let imageService: ImageService;
  let dbManager: DatabaseManager;
  let settingsService: SettingsService;
  let testDb: Database.Database;
  let tempStorageDir: string;

  beforeEach(async () => {
    // 设置测试数据库
    const testDbSetup = createTestDatabase();
    testDb = testDbSetup.db;
    dbManager = mockDatabaseManager(testDb) as any;
    
    // 设置临时存储目录
    tempStorageDir = path.join(__dirname, '../../../test-temp/delete-performance-test');
    
    // Mock SettingsService
    settingsService = {
      getStoragePath: vi.fn(() => tempStorageDir),
      usesCategoryFolders: vi.fn(() => true),
      getSettings: vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'local' as 'local',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })),
      saveSettings: vi.fn(() => true),
      getOSSConfig: vi.fn(() => null),
      updateOSSConfig: vi.fn(() => true),
      clearOSSConfig: vi.fn(() => true),
      getStorageType: vi.fn(() => 'local')
    } as any;

    // 创建ImageService实例
    imageService = new ImageService(dbManager, settingsService);

    // Mock fs operations
    mockFs.existsSync.mockReturnValue(true);
    mockFs.unlinkSync.mockImplementation(() => {});
    
    console.log('✅ 删除性能测试环境初始化完成');
  });

  afterEach(async () => {
    cleanupTestDatabase();
    vi.clearAllMocks();
  });

  describe('批量删除性能测试', () => {
    it('应该在合理时间内完成大量图片的批量删除', async () => {
      const imageCount = 100;
      const maxTimeMs = 5000; // 5秒内完成100张图片的删除

      // 创建测试分类
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      // 批量创建图片记录
      const images = Array.from({ length: imageCount }, () => TestDataGenerator.createImage(category.id));
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const insertTransaction = testDb.transaction(() => {
        for (const image of images) {
          insertImage.run(
            image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
            image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
            image.description, image.created_at, image.updated_at, 
            JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
          );
        }
      });
      insertTransaction();

      const imageIds = images.map(img => img.id);

      // 执行性能测试
      const startTime = Date.now();
      const result = await imageService.deleteImages(imageIds);
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // 验证结果
      expect(result.totalCount).toBe(imageCount);
      expect(result.successCount).toBe(imageCount);
      expect(result.failedCount).toBe(0);

      // 验证性能
      expect(executionTime).toBeLessThan(maxTimeMs);
      console.log(`✅ 批量删除 ${imageCount} 张图片耗时: ${executionTime}ms`);

      // 计算性能指标
      const imagesPerSecond = (imageCount / executionTime) * 1000;
      expect(imagesPerSecond).toBeGreaterThan(10); // 至少每秒删除10张图片
      console.log(`📊 删除性能: ${imagesPerSecond.toFixed(2)} 张/秒`);
    });

    it('应该在内存使用合理的情况下处理大量删除', async () => {
      const imageCount = 50;
      
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const images = Array.from({ length: imageCount }, () => TestDataGenerator.createImage(category.id));
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const insertTransaction = testDb.transaction(() => {
        for (const image of images) {
          insertImage.run(
            image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
            image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
            image.description, image.created_at, image.updated_at, 
            JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
          );
        }
      });
      insertTransaction();

      // 监控内存使用
      const initialMemory = process.memoryUsage();
      
      const imageIds = images.map(img => img.id);
      const result = await imageService.deleteImages(imageIds);
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      // 验证结果
      expect(result.successCount).toBe(imageCount);

      // 验证内存使用（不应该增长超过50MB）
      const maxMemoryIncreaseMB = 50;
      const memoryIncreaseMB = memoryIncrease / (1024 * 1024);
      expect(memoryIncreaseMB).toBeLessThan(maxMemoryIncreaseMB);
      
      console.log(`📊 内存增长: ${memoryIncreaseMB.toFixed(2)}MB`);
    });
  });

  describe('OSS删除超时处理测试', () => {
    beforeEach(() => {
      // 设置OSS存储模式
      settingsService.getSettings = vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'oss' as 'oss',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      // 重新创建ImageService
      imageService = new ImageService(dbManager, settingsService);
    });

    it('应该处理OSS删除超时的情况', async () => {
      // Mock OSS服务，模拟超时
      const mockOSSService = {
        isConfigured: vi.fn(() => true),
        deleteFile: vi.fn(() => new Promise((_, reject) => {
          setTimeout(() => reject(new Error('删除超时')), 100);
        }))
      };
      (imageService as any).ossService = mockOSSService;

      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const startTime = Date.now();
      const result = await imageService.deleteImage(image.id);
      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // 验证超时处理
      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true); // 数据库删除应该成功
      expect(result.error).toContain('删除超时');

      // 验证操作在合理时间内完成（不应该无限等待）
      expect(executionTime).toBeLessThan(1000); // 1秒内完成
      
      console.log(`⏱️ OSS超时处理耗时: ${executionTime}ms`);
    });
  });

  describe('性能基准测试', () => {
    it('应该建立单个图片删除的性能基准', async () => {
      const iterations = 10;
      const maxTimePerDeleteMs = 200; // 每次删除不超过200ms

      // 创建测试分类
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const executionTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        // 创建图片
        const image = TestDataGenerator.createImage(category.id);
        const insertImage = testDb.prepare(`
          INSERT INTO images (id, category_id, title, original_filename, stored_filename,
            relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
            description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
          image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
          image.description, image.created_at, image.updated_at, 
          JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
        );

        // 测试删除性能
        const startTime = Date.now();
        const result = await imageService.deleteImage(image.id);
        const endTime = Date.now();
        const executionTime = endTime - startTime;

        executionTimes.push(executionTime);
        expect(result.success).toBe(true);
        expect(executionTime).toBeLessThan(maxTimePerDeleteMs);
      }

      // 计算性能统计
      const avgTime = executionTimes.reduce((a, b) => a + b, 0) / executionTimes.length;
      const maxTime = Math.max(...executionTimes);
      const minTime = Math.min(...executionTimes);

      console.log(`📊 单个删除性能基准 (${iterations}次测试):`);
      console.log(`   平均时间: ${avgTime.toFixed(2)}ms`);
      console.log(`   最大时间: ${maxTime}ms`);
      console.log(`   最小时间: ${minTime}ms`);

      // 验证性能基准
      expect(avgTime).toBeLessThan(maxTimePerDeleteMs);
      expect(maxTime).toBeLessThan(maxTimePerDeleteMs * 2); // 最大时间不超过平均时间的2倍
    });
  });
});
