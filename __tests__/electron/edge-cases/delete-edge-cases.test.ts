import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ImageService } from '../../../electron/services/ImageService';
import { DatabaseManager } from '../../../electron/database';
import { SettingsService } from '../../../electron/services/SettingsService';
import { TestDataGenerator } from '../helpers/test-data-generator';
import { setupTestDatabase, cleanupTestDatabase } from '../helpers/test-database';
import Database from 'better-sqlite3';
import * as fs from 'fs';
import * as path from 'path';

// Mock Electron modules
vi.mock('electron', async () => {
  const { createElectronMocks } = await import('../helpers/electron-mocks');
  return createElectronMocks();
});

// Mock fs operations for testing
vi.mock('fs');
const mockFs = vi.mocked(fs);

describe('删除功能边界情况测试', () => {
  let imageService: ImageService;
  let dbManager: DatabaseManager;
  let settingsService: SettingsService;
  let testDb: Database.Database;
  let tempStorageDir: string;

  beforeEach(async () => {
    // 设置测试数据库
    const dbSetup = await setupTestDatabase();
    dbManager = dbSetup.dbManager;
    testDb = dbSetup.testDb;
    
    // 设置临时存储目录
    tempStorageDir = path.join(__dirname, '../../../test-temp/delete-edge-cases-test');
    
    // Mock SettingsService
    settingsService = {
      getStoragePath: vi.fn(() => tempStorageDir),
      usesCategoryFolders: vi.fn(() => true),
      getSettings: vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'local' as 'local',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })),
      saveSettings: vi.fn(() => true)
    } as any;

    // 创建ImageService实例
    imageService = new ImageService(dbManager, settingsService);

    // Mock fs operations
    mockFs.existsSync.mockReturnValue(true);
    mockFs.unlinkSync.mockImplementation(() => {});
    
    console.log('✅ 删除边界情况测试环境初始化完成');
  });

  afterEach(async () => {
    await cleanupTestDatabase();
    vi.clearAllMocks();
  });

  describe('删除不存在的图片', () => {
    it('应该优雅处理删除不存在的图片', async () => {
      const nonExistentId = 'non-existent-image-id';
      
      const result = await imageService.deleteImage(nonExistentId);

      expect(result.success).toBe(false);
      expect(result.imageId).toBe(nonExistentId);
      expect(result.error).toContain('删除验证失败');
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(false);
    });

    it('应该处理批量删除中包含不存在图片的情况', async () => {
      // 创建一张存在的图片
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const existingImage = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        existingImage.id, existingImage.category_id, existingImage.title, existingImage.original_filename, existingImage.stored_filename,
        existingImage.relative_file_path, existingImage.relative_thumbnail_path, existingImage.mime_type, existingImage.size_bytes,
        existingImage.description, existingImage.created_at, existingImage.updated_at, 
        JSON.stringify(existingImage.file_metadata), existingImage.image_url, existingImage.thumbnail_url
      );

      const imageIds = [existingImage.id, 'non-existent-1', 'non-existent-2'];
      const result = await imageService.deleteImages(imageIds);

      expect(result.totalCount).toBe(3);
      expect(result.successCount).toBe(1);
      expect(result.failedCount).toBe(2);
      expect(result.errors.length).toBeGreaterThan(0);

      // 验证存在的图片被成功删除
      const deletedImage = await imageService.getImageById(existingImage.id);
      expect(deletedImage).toBeNull();
    });
  });

  describe('网络断开时的OSS删除', () => {
    beforeEach(() => {
      // 设置OSS存储模式
      settingsService.getSettings = vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'oss' as 'oss',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      // 重新创建ImageService
      imageService = new ImageService(dbManager, settingsService);
    });

    it('应该处理网络连接失败的情况', async () => {
      // Mock OSS服务，模拟网络错误
      const mockOSSService = {
        isConfigured: vi.fn(() => true),
        deleteFile: vi.fn(() => Promise.reject(new Error('Network Error: ENOTFOUND')))
      };
      (imageService as any).ossService = mockOSSService;

      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true); // 数据库删除应该成功
      expect(result.error).toContain('Network Error');

      // 验证数据库记录被删除（即使OSS删除失败）
      const deletedImage = await imageService.getImageById(image.id);
      expect(deletedImage).toBeNull();
    });

    it('应该处理OSS服务不可用的情况', async () => {
      // Mock OSS服务未配置
      const mockOSSService = {
        isConfigured: vi.fn(() => false),
        deleteFile: vi.fn()
      };
      (imageService as any).ossService = mockOSSService;

      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true);
      expect(result.error).toContain('OSS未配置');

      // 验证OSS删除方法没有被调用
      expect(mockOSSService.deleteFile).not.toHaveBeenCalled();
    });
  });

  describe('磁盘空间不足时的删除', () => {
    it('应该处理本地文件删除时的权限错误', async () => {
      // Mock文件删除权限错误
      mockFs.unlinkSync.mockImplementation(() => {
        throw new Error('EACCES: permission denied');
      });

      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true);
      expect(result.error).toContain('permission denied');
    });

    it('应该处理文件系统只读的情况', async () => {
      // Mock文件系统只读错误
      mockFs.unlinkSync.mockImplementation(() => {
        throw new Error('EROFS: read-only file system');
      });

      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true);
      expect(result.error).toContain('read-only file system');
    });
  });

  describe('并发删除同一图片的情况', () => {
    it('应该处理并发删除同一图片的竞态条件', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      // 并发执行多个删除操作
      const deletePromises = [
        imageService.deleteImage(image.id),
        imageService.deleteImage(image.id),
        imageService.deleteImage(image.id)
      ];

      const results = await Promise.all(deletePromises);

      // 至少有一个删除操作应该成功
      const successfulDeletes = results.filter(r => r.success);
      const failedDeletes = results.filter(r => !r.success);

      expect(successfulDeletes.length).toBeGreaterThanOrEqual(1);
      
      // 失败的删除应该是因为图片已经不存在
      for (const failedResult of failedDeletes) {
        expect(failedResult.error).toContain('删除验证失败');
      }

      // 验证图片最终被删除
      const deletedImage = await imageService.getImageById(image.id);
      expect(deletedImage).toBeNull();
    });
  });

  describe('数据库异常情况', () => {
    it('应该处理数据库连接丢失的情况', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      // Mock数据库错误
      const originalPrepare = testDb.prepare;
      testDb.prepare = vi.fn(() => {
        throw new Error('Database connection lost');
      });

      try {
        const result = await imageService.deleteImage(image.id);
        
        expect(result.success).toBe(false);
        expect(result.databaseDeleted).toBe(false);
        expect(result.error).toContain('Database connection lost');
      } finally {
        // 恢复数据库连接
        testDb.prepare = originalPrepare;
      }
    });
  });
});
