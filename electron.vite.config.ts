import { defineConfig } from 'electron-vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { copyFileSync, existsSync, mkdirSync } from 'fs';

// 创建一个插件来复制HTML文件
const copyHtmlPlugin = () => {
  return {
    name: 'copy-html',
    writeBundle() {
      const sourceHtml = resolve(__dirname, 'electron/oss-config-window.html');
      const targetDir = resolve(__dirname, 'dist-electron/main');
      const targetHtml = resolve(targetDir, 'oss-config-window.html');
      
      if (!existsSync(targetDir)) {
        mkdirSync(targetDir, { recursive: true });
      }
      
      if (existsSync(sourceHtml)) {
        copyFileSync(sourceHtml, targetHtml);
        console.log('✅ 复制HTML文件:', targetHtml);
      } else {
        console.warn('⚠️  HTML文件不存在:', sourceHtml);
      }
    }
  };
};

export default defineConfig({
  main: {
    plugins: [copyHtmlPlugin()],
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'electron/main.ts'),
        external: ['better-sqlite3', 'uuid', 'sharp']
      },
      outDir: 'dist-electron/main'
    }
  },
  preload: {
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'electron/preload.ts'),
        output: {
          format: 'cjs',
          entryFileNames: 'preload.js'
        }
      },
      outDir: 'dist-electron/preload'
    }
  },
  renderer: {
    plugins: [react()],
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'index.html')
      },
      outDir: 'dist-electron/renderer'
    },
    root: '.',
    publicDir: 'public',
    server: {
      port: 5173
    }
  }
});