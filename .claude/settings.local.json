{"permissions": {"allow": ["<PERSON><PERSON>(mv:*)", "Bash(find:*)", "WebFetch(domain:vitest.dev)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run test:run:*)", "Bash(npm run test:coverage:*)", "Bash(npm test:*)", "Bash(rm:*)", "Bash(node:*)", "Bash(npx vitest:*)", "<PERSON><PERSON>(echo:*)", "Bash(echo $LANG)", "Bash(echo $LC_ALL)", "Bash(fc-list:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(sudo apt install:*)", "Bash(npm rebuild:*)", "Bash(npm run electron:build:*)", "Bash(npm run electron:start:*)", "Bash(npx:*)", "Bash(npm:*)", "Bash(ls:*)", "<PERSON>sh(timeout 15 npm run electron:start)", "Bash(timeout 10 npm run electron:start)"], "deny": []}}