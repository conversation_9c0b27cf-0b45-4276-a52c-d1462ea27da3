{"short_name": "Pokedex IM", "name": "Pokedex Image Manager", "description": "A web application for managing and displaying images in a Pokedex-style, allowing categorization, image uploads, metadata editing, and species information lookup. Built with React and Tailwind CSS.", "icons": [{"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}], "start_url": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#3B82F6", "background_color": "#F1F5F9", "scope": "/", "lang": "zh-CN", "dir": "auto"}